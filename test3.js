auto.waitFor();
requestScreenCapture();

// 创建悬浮窗显示坐标
let floaty = floaty.window(
    <frame gravity="center">
        <text id="text" textSize="16sp" textColor="red"/>
    </frame>
);
floaty.text.setText("请点击屏幕任意位置");

// 设置触摸监听
function setOnTouchListener(event){
    // 获取触摸坐标
    let x = event.getX();
    let y = event.getY();
    
    // 更新悬浮窗显示
    floaty.text.setText("X: " + x + "\nY: " + y);
    
    // 在控制台输出坐标
    console.log("点击坐标: (" + x + ", " + y + ")");
    
    // 保持悬浮窗在触摸位置附近
    floaty.setPosition(x + 20, y + 20);
    
    // 返回true表示消费此事件
    return true;
};

// 提示信息
toast("请点击屏幕任意位置获取坐标\n长按结束脚本");
sleep(3000);

// 长按退出
let exit = false;
threads.start(function(){
    events.observeKey();
    events.onKeyDown("volume_down", function(event){
        exit = true;
    });
});

// 保持脚本运行
while(!exit){
    sleep(1000);
}
floaty.close();
toast("脚本已结束");