/**
 * Auto.js 获取屏幕坐标的完整演示
 * 包含多种坐标获取方式和实用功能
 */

auto.waitFor();
requestScreenCapture();

// 全局变量
var coordinateHistory = [];
var isRecording = false;
var currentMode = "touch";
var floatyWindow = null;

// 配置参数
var CONFIG = {
    FLOATY_WIDTH: 200,
    FLOATY_HEIGHT: 100,
    MAX_HISTORY: 50,
    COLORS: {
        PRIMARY: "#FF4444",
        SUCCESS: "#44FF44",
        INFO: "#4444FF",
        WARNING: "#FFAA44"
    }
};

// 创建主悬浮窗
function createMainFloaty() {
    floatyWindow = floaty.window(
        <vertical bg="#88000000" padding="8dp">
            <text id="title" text="坐标获取工具" textColor="#FFFFFF" textSize="14sp" gravity="center"/>
            <text id="coordinates" text="坐标: (0, 0)" textColor="#44FF44" textSize="12sp" gravity="center"/>
            <text id="mode" text="模式: 触摸" textColor="#FFAA44" textSize="10sp" gravity="center"/>
            <text id="count" text="记录: 0" textColor="#4444FF" textSize="10sp" gravity="center"/>
        </vertical>
    );

    floatyWindow.setPosition(50, 100);
    floatyWindow.setSize(CONFIG.FLOATY_WIDTH, CONFIG.FLOATY_HEIGHT);

    // 设置悬浮窗可拖拽
    floatyWindow.setTouchable(true);

    return floatyWindow;
}

// 更新悬浮窗显示
function updateFloatyDisplay(x, y, mode, count) {
    if (floatyWindow) {
        ui.run(function() {
            floatyWindow.coordinates.setText("坐标: (" + Math.round(x) + ", " + Math.round(y) + ")");
            floatyWindow.mode.setText("模式: " + mode);
            floatyWindow.count.setText("记录: " + count);
        });
    }
}

// 记录坐标到历史
function recordCoordinate(x, y, type, elementInfo) {
    var record = {
        x: Math.round(x),
        y: Math.round(y),
        type: type || "touch",
        timestamp: new Date().toLocaleTimeString(),
        elementInfo: elementInfo || null
    };

    coordinateHistory.push(record);

    // 限制历史记录数量
    if (coordinateHistory.length > CONFIG.MAX_HISTORY) {
        coordinateHistory.shift();
    }

    console.log("记录坐标: " + JSON.stringify(record));
    return record;
}

// 方法1: 触摸监听获取坐标
function startTouchListener() {
    currentMode = "触摸监听";
    toast("触摸监听模式已启动");

    events.observeTouch();
    events.onTouch(function(point) {
        if (point.action == "down") {
            var record = recordCoordinate(point.x, point.y, "touch");
            updateFloatyDisplay(point.x, point.y, currentMode, coordinateHistory.length);

            // 播放提示音
            device.vibrate(50);
        } else if (point.action == "move") {
            updateFloatyDisplay(point.x, point.y, currentMode, coordinateHistory.length);
        }
    });
}

// 方法2: 元素坐标获取
function getElementCoordinates(elementText) {
    currentMode = "元素定位";
    toast("正在查找元素: " + elementText);

    try {
        // 多种方式查找元素
        var element = text(elementText).findOne(3000) ||
                     desc(elementText).findOne(3000) ||
                     textContains(elementText).findOne(3000) ||
                     descContains(elementText).findOne(3000);

        if (element) {
            var bounds = element.bounds();
            var centerX = bounds.centerX();
            var centerY = bounds.centerY();

            var elementInfo = {
                text: element.text() || element.desc() || "无文本",
                className: element.className(),
                bounds: {
                    left: bounds.left,
                    top: bounds.top,
                    right: bounds.right,
                    bottom: bounds.bottom,
                    width: bounds.width(),
                    height: bounds.height()
                }
            };

            var record = recordCoordinate(centerX, centerY, "element", elementInfo);
            updateFloatyDisplay(centerX, centerY, currentMode, coordinateHistory.length);

            toast("找到元素: " + elementInfo.text);
            console.log("元素详细信息: " + JSON.stringify(elementInfo, null, 2));

            return record;
        } else {
            toast("未找到元素: " + elementText);
            return null;
        }
    } catch (error) {
        toast("查找元素出错: " + error.message);
        return null;
    }
}

// 方法3: 批量获取所有可点击元素坐标
function getAllClickableElements() {
    currentMode = "批量扫描";
    toast("正在扫描所有可点击元素...");

    try {
        var clickableElements = clickable(true).find();
        var results = [];

        for (var i = 0; i < Math.min(clickableElements.length, 20); i++) {
            var element = clickableElements[i];
            var bounds = element.bounds();
            var centerX = bounds.centerX();
            var centerY = bounds.centerY();

            var elementInfo = {
                index: i,
                text: element.text() || element.desc() || "元素" + i,
                className: element.className(),
                bounds: {
                    left: bounds.left,
                    top: bounds.top,
                    right: bounds.right,
                    bottom: bounds.bottom
                }
            };

            var record = recordCoordinate(centerX, centerY, "clickable", elementInfo);
            results.push(record);
        }

        updateFloatyDisplay(0, 0, currentMode, coordinateHistory.length);
        toast("扫描完成，找到 " + results.length + " 个可点击元素");

        return results;
    } catch (error) {
        toast("扫描元素出错: " + error.message);
        return [];
    }
}

// 方法4: 颜色定位获取坐标
function getCoordinatesByColor(color) {
    currentMode = "颜色定位";
    toast("正在查找颜色: " + color);

    try {
        var img = captureScreen();
        if (!img) {
            toast("截图失败");
            return null;
        }

        var point = findColor(img, color);
        if (point) {
            var record = recordCoordinate(point.x, point.y, "color", {color: color});
            updateFloatyDisplay(point.x, point.y, currentMode, coordinateHistory.length);

            toast("找到颜色位置: (" + point.x + ", " + point.y + ")");
            return record;
        } else {
            toast("未找到指定颜色");
            return null;
        }
    } catch (error) {
        toast("颜色定位出错: " + error.message);
        return null;
    }
}

// 方法5: 图像识别获取坐标
function getCoordinatesByImage(templatePath) {
    currentMode = "图像识别";
    toast("正在进行图像识别...");

    try {
        var img = captureScreen();
        var template = images.read(templatePath);

        if (!img || !template) {
            toast("图像读取失败");
            return null;
        }

        var result = findImage(img, template);
        if (result) {
            var centerX = result.x + template.getWidth() / 2;
            var centerY = result.y + template.getHeight() / 2;

            var record = recordCoordinate(centerX, centerY, "image", {
                templatePath: templatePath,
                matchPoint: result
            });

            updateFloatyDisplay(centerX, centerY, currentMode, coordinateHistory.length);
            toast("找到图像位置: (" + Math.round(centerX) + ", " + Math.round(centerY) + ")");

            return record;
        } else {
            toast("未找到匹配图像");
            return null;
        }
    } catch (error) {
        toast("图像识别出错: " + error.message);
        return null;
    }
}

// 坐标历史管理
function showCoordinateHistory() {
    if (coordinateHistory.length == 0) {
        toast("暂无坐标记录");
        return;
    }

    console.log("=== 坐标历史记录 ===");
    for (var i = 0; i < coordinateHistory.length; i++) {
        var record = coordinateHistory[i];
        console.log((i + 1) + ". [" + record.timestamp + "] (" + record.x + ", " + record.y + ") - " + record.type);
        if (record.elementInfo) {
            console.log("   元素信息: " + JSON.stringify(record.elementInfo.text || record.elementInfo));
        }
    }

    toast("坐标历史已输出到控制台，共 " + coordinateHistory.length + " 条记录");
}

// 清空坐标历史
function clearCoordinateHistory() {
    coordinateHistory = [];
    updateFloatyDisplay(0, 0, currentMode, 0);
    toast("坐标历史已清空");
}

// 导出坐标数据
function exportCoordinates() {
    if (coordinateHistory.length == 0) {
        toast("暂无数据可导出");
        return;
    }

    try {
        var exportData = {
            exportTime: new Date().toLocaleString(),
            totalCount: coordinateHistory.length,
            coordinates: coordinateHistory
        };

        var jsonData = JSON.stringify(exportData, null, 2);
        var fileName = "/sdcard/coordinates_" + Date.now() + ".json";

        files.write(fileName, jsonData);
        toast("坐标数据已导出到: " + fileName);
        console.log("导出文件: " + fileName);

    } catch (error) {
        toast("导出失败: " + error.message);
    }
}

// 测试点击坐标
function testClickCoordinate(x, y) {
    try {
        toast("即将点击坐标: (" + x + ", " + y + ")");
        sleep(1000);

        // 在坐标位置显示标记
        var marker = floaty.window(
            <frame bg="#FFFF0000" alpha="0.8">
                <text text="●" textColor="#FFFFFF" textSize="20sp"/>
            </frame>
        );
        marker.setPosition(x - 10, y - 10);
        marker.setSize(20, 20);

        sleep(500);
        click(x, y);
        sleep(500);

        marker.close();
        toast("已点击坐标: (" + x + ", " + y + ")");

    } catch (error) {
        toast("点击测试失败: " + error.message);
    }
}

// 屏幕信息获取
function getScreenInfo() {
    var screenInfo = {
        width: device.width,
        height: device.height,
        density: context.getResources().getDisplayMetrics().density,
        densityDpi: context.getResources().getDisplayMetrics().densityDpi,
        scaledDensity: context.getResources().getDisplayMetrics().scaledDensity
    };

    console.log("=== 屏幕信息 ===");
    console.log("宽度: " + screenInfo.width + "px");
    console.log("高度: " + screenInfo.height + "px");
    console.log("密度: " + screenInfo.density);
    console.log("DPI: " + screenInfo.densityDpi);
    console.log("缩放密度: " + screenInfo.scaledDensity);

    toast("屏幕信息已输出到控制台");
    return screenInfo;
}

// 创建控制面板
function createControlPanel() {
    var controlPanel = floaty.window(
        <vertical bg="#CC000000" padding="5dp">
            <text text="坐标工具控制面板" textColor="#FFFFFF" textSize="12sp" gravity="center"/>
            <horizontal>
                <button id="touchBtn" text="触摸" textSize="10sp" style="Widget.AppCompat.Button.Colored"/>
                <button id="elementBtn" text="元素" textSize="10sp" style="Widget.AppCompat.Button.Colored"/>
            </horizontal>
            <horizontal>
                <button id="scanBtn" text="扫描" textSize="10sp" style="Widget.AppCompat.Button.Colored"/>
                <button id="historyBtn" text="历史" textSize="10sp" style="Widget.AppCompat.Button.Colored"/>
            </horizontal>
            <horizontal>
                <button id="clearBtn" text="清空" textSize="10sp" style="Widget.AppCompat.Button.Colored"/>
                <button id="exportBtn" text="导出" textSize="10sp" style="Widget.AppCompat.Button.Colored"/>
            </horizontal>
        </vertical>
    );

    controlPanel.setPosition(device.width - 200, 200);

    // 按钮事件处理
    controlPanel.touchBtn.click(function() {
        startTouchListener();
    });

    controlPanel.elementBtn.click(function() {
        var elementText = dialogs.rawInput("输入要查找的元素文本", "");
        if (elementText) {
            getElementCoordinates(elementText);
        }
    });

    controlPanel.scanBtn.click(function() {
        getAllClickableElements();
    });

    controlPanel.historyBtn.click(function() {
        showCoordinateHistory();
    });

    controlPanel.clearBtn.click(function() {
        clearCoordinateHistory();
    });

    controlPanel.exportBtn.click(function() {
        exportCoordinates();
    });

    return controlPanel;
}

// 主菜单函数
function showMainMenu() {
    var choice = dialogs.select("Auto.js 坐标获取工具", [
        "启动触摸监听模式",
        "查找元素坐标",
        "扫描所有可点击元素",
        "颜色定位",
        "显示坐标历史",
        "测试点击坐标",
        "获取屏幕信息",
        "清空历史记录",
        "导出坐标数据",
        "启动控制面板模式",
        "退出"
    ]);

    return choice;
}

// 主程序入口
function main() {
    try {
        // 创建主悬浮窗
        createMainFloaty();

        // 显示欢迎信息
        toast("Auto.js 坐标获取工具已启动");
        console.log("=== Auto.js 坐标获取工具 ===");
        console.log("功能: 多种方式获取屏幕坐标");
        console.log("作者: Auto.js Demo");
        console.log("版本: 1.0");

        var running = true;

        while (running) {
            var choice = showMainMenu();

            switch (choice) {
                case 0: // 启动触摸监听模式
                    startTouchListener();
                    toast("触摸屏幕任意位置获取坐标，音量下键停止");

                    // 等待用户停止
                    var stopTouch = false;
                    events.observeKey();
                    events.onKeyDown("volume_down", function() {
                        stopTouch = true;
                        events.removeAllListeners();
                    });

                    while (!stopTouch) {
                        sleep(1000);
                    }

                    toast("触摸监听已停止");
                    break;

                case 1: // 查找元素坐标
                    var elementText = dialogs.rawInput("输入要查找的元素文本", "微信");
                    if (elementText) {
                        getElementCoordinates(elementText);
                    }
                    break;

                case 2: // 扫描所有可点击元素
                    getAllClickableElements();
                    break;

                case 3: // 颜色定位
                    var color = dialogs.rawInput("输入颜色值(如: #FF0000)", "#FF0000");
                    if (color) {
                        getCoordinatesByColor(color);
                    }
                    break;

                case 4: // 显示坐标历史
                    showCoordinateHistory();
                    break;

                case 5: // 测试点击坐标
                    var x = parseInt(dialogs.rawInput("输入X坐标", "500"));
                    var y = parseInt(dialogs.rawInput("输入Y坐标", "1000"));
                    if (!isNaN(x) && !isNaN(y)) {
                        testClickCoordinate(x, y);
                    }
                    break;

                case 6: // 获取屏幕信息
                    getScreenInfo();
                    break;

                case 7: // 清空历史记录
                    clearCoordinateHistory();
                    break;

                case 8: // 导出坐标数据
                    exportCoordinates();
                    break;

                case 9: // 启动控制面板模式
                    var controlPanel = createControlPanel();
                    startTouchListener();

                    toast("控制面板模式已启动\n触摸屏幕获取坐标\n音量下键退出");

                    var exitPanel = false;
                    events.observeKey();
                    events.onKeyDown("volume_down", function() {
                        exitPanel = true;
                        events.removeAllListeners();
                        controlPanel.close();
                    });

                    while (!exitPanel) {
                        sleep(1000);
                    }

                    toast("控制面板模式已退出");
                    break;

                case 10: // 退出
                    running = false;
                    break;

                default:
                    toast("未选择操作");
                    break;
            }

            if (running && choice != 0 && choice != 9) {
                sleep(1000); // 给用户时间查看结果
            }
        }

    } catch (error) {
        toast("程序出错: " + error.message);
        console.error("主程序错误:", error);
    } finally {
        // 清理资源
        if (floatyWindow) {
            floatyWindow.close();
        }
        events.removeAllListeners();
        toast("坐标获取工具已退出");
    }
}

// 启动程序
main();