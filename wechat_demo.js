/**
 * Auto.js 微信操作演示脚本
 * 功能包括：启动微信、发送消息、查看朋友圈、操作公众号等
 */

// 请求必要的权限
auto.waitFor();
requestScreenCapture();

// 配置参数
const CONFIG = {
    WECHAT_PACKAGE: "com.tencent.mm",
    WAIT_TIME: {
        SHORT: 1000,
        MEDIUM: 2000,
        LONG: 3000,
        EXTRA_LONG: 5000
    }
};

// 工具函数类
class WeChatUtils {
    
    // 启动微信
    static async launchWeChat() {
        try {
            toast("正在启动微信...");
            launchApp(CONFIG.WECHAT_PACKAGE);
            
            // 等待微信启动
            if (!this.waitForApp(CONFIG.WECHAT_PACKAGE, 10000)) {
                throw new Error("微信启动失败");
            }
            
            sleep(CONFIG.WAIT_TIME.LONG);
            toast("微信启动成功");
            return true;
        } catch (error) {
            toast("启动微信失败: " + error.message);
            return false;
        }
    }
    
    // 等待应用启动
    static waitForApp(packageName, timeout) {
        let startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            if (currentPackage() === packageName) {
                return true;
            }
            sleep(500);
        }
        return false;
    }
    
    // 智能点击元素
    static clickElement(selector, timeout = 3000) {
        let element = null;
        
        // 尝试多种选择器
        if (typeof selector === 'string') {
            element = text(selector).findOne(timeout) || 
                     desc(selector).findOne(timeout) ||
                     textContains(selector).findOne(timeout);
        } else if (typeof selector === 'object') {
            element = selector.findOne ? selector.findOne(timeout) : selector;
        }
        
        if (element && element.clickable()) {
            element.click();
            return true;
        } else if (element) {
            // 查找可点击的父元素
            let parent = element.parent();
            while (parent && !parent.clickable()) {
                parent = parent.parent();
            }
            if (parent) {
                parent.click();
                return true;
            }
        }
        
        return false;
    }
    
    // 切换到指定标签页
    static switchToTab(tabName) {
        toast(`切换到${tabName}页面`);
        if (this.clickElement(tabName)) {
            sleep(CONFIG.WAIT_TIME.MEDIUM);
            return true;
        }
        toast(`无法找到${tabName}标签`);
        return false;
    }
    
    // 搜索功能
    static searchInWeChat(keyword) {
        try {
            // 点击搜索按钮
            if (!this.clickElement("搜索") && !this.clickElement(id("search_btn"))) {
                toast("未找到搜索按钮");
                return false;
            }
            
            sleep(CONFIG.WAIT_TIME.MEDIUM);
            
            // 输入搜索关键词
            let searchBox = className("EditText").findOne(3000);
            if (searchBox) {
                searchBox.setText(keyword);
                sleep(CONFIG.WAIT_TIME.SHORT);
                
                // 点击搜索或回车
                key(66); // 回车键
                sleep(CONFIG.WAIT_TIME.MEDIUM);
                
                toast(`搜索关键词: ${keyword}`);
                return true;
            }
            
            toast("未找到搜索输入框");
            return false;
        } catch (error) {
            toast("搜索失败: " + error.message);
            return false;
        }
    }
}

// 聊天功能类
class ChatOperations {
    
    // 发送消息给指定联系人
    static sendMessage(contactName, message) {
        try {
            toast(`准备给${contactName}发送消息`);
            
            // 确保在微信主页面
            WeChatUtils.switchToTab("微信");
            
            // 搜索联系人
            if (!WeChatUtils.searchInWeChat(contactName)) {
                return false;
            }
            
            // 点击联系人
            let contact = text(contactName).findOne(3000) || textContains(contactName).findOne(3000);
            if (!contact) {
                toast(`未找到联系人: ${contactName}`);
                return false;
            }
            
            contact.click();
            sleep(CONFIG.WAIT_TIME.MEDIUM);
            
            // 输入消息
            let inputBox = className("EditText").findOne(3000);
            if (!inputBox) {
                toast("未找到输入框");
                return false;
            }
            
            inputBox.setText(message);
            sleep(CONFIG.WAIT_TIME.SHORT);
            
            // 发送消息
            let sendBtn = text("发送").findOne(2000) || desc("发送").findOne(2000);
            if (sendBtn) {
                sendBtn.click();
                toast(`消息已发送给${contactName}: ${message}`);
                return true;
            }
            
            toast("未找到发送按钮");
            return false;
            
        } catch (error) {
            toast("发送消息失败: " + error.message);
            return false;
        }
    }
    
    // 群发消息
    static sendGroupMessage(groupName, message) {
        try {
            toast(`准备给群${groupName}发送消息`);
            
            // 切换到微信页面
            WeChatUtils.switchToTab("微信");
            
            // 搜索群聊
            if (!WeChatUtils.searchInWeChat(groupName)) {
                return false;
            }
            
            // 查找并点击群聊
            let group = textContains(groupName).findOne(3000);
            if (!group) {
                toast(`未找到群聊: ${groupName}`);
                return false;
            }
            
            group.click();
            sleep(CONFIG.WAIT_TIME.MEDIUM);
            
            // 发送消息
            return this.sendMessageInCurrentChat(message);
            
        } catch (error) {
            toast("群发消息失败: " + error.message);
            return false;
        }
    }
    
    // 在当前聊天窗口发送消息
    static sendMessageInCurrentChat(message) {
        try {
            let inputBox = className("EditText").findOne(3000);
            if (!inputBox) {
                toast("未找到输入框");
                return false;
            }
            
            inputBox.setText(message);
            sleep(CONFIG.WAIT_TIME.SHORT);
            
            // 点击发送
            let sendBtn = text("发送").findOne(2000) || desc("发送").findOne(2000);
            if (sendBtn) {
                sendBtn.click();
                toast("消息发送成功: " + message);
                return true;
            }
            
            toast("未找到发送按钮");
            return false;
            
        } catch (error) {
            toast("发送消息失败: " + error.message);
            return false;
        }
    }
}

// 朋友圈操作类
class MomentsOperations {
    
    // 查看朋友圈
    static viewMoments() {
        try {
            toast("正在打开朋友圈");
            
            // 切换到发现页面
            if (!WeChatUtils.switchToTab("发现")) {
                return false;
            }
            
            // 点击朋友圈
            if (!WeChatUtils.clickElement("朋友圈")) {
                toast("未找到朋友圈入口");
                return false;
            }
            
            sleep(CONFIG.WAIT_TIME.LONG);
            toast("已进入朋友圈");
            return true;
            
        } catch (error) {
            toast("打开朋友圈失败: " + error.message);
            return false;
        }
    }
    
    // 点赞朋友圈
    static likeMoments(count = 3) {
        try {
            if (!this.viewMoments()) {
                return false;
            }
            
            toast(`准备为前${count}条朋友圈点赞`);
            let likedCount = 0;
            
            for (let i = 0; i < count; i++) {
                // 查找点赞按钮
                let likeButtons = desc("赞").find();
                if (likeButtons.length > i) {
                    likeButtons[i].click();
                    likedCount++;
                    toast(`已点赞第${i + 1}条朋友圈`);
                    sleep(CONFIG.WAIT_TIME.SHORT);
                }
                
                // 滑动查看更多
                if (i < count - 1) {
                    swipe(device.width / 2, device.height * 0.8, 
                          device.width / 2, device.height * 0.3, 1000);
                    sleep(CONFIG.WAIT_TIME.MEDIUM);
                }
            }
            
            toast(`共点赞了${likedCount}条朋友圈`);
            return true;
            
        } catch (error) {
            toast("点赞朋友圈失败: " + error.message);
            return false;
        }
    }
}

// 公众号操作类
class PublicAccountOperations {

    // 打开指定公众号
    static openPublicAccount(accountName) {
        try {
            toast(`正在打开公众号: ${accountName}`);

            // 切换到通讯录
            if (!WeChatUtils.switchToTab("通讯录")) {
                return false;
            }

            // 点击公众号
            if (!WeChatUtils.clickElement("公众号")) {
                toast("未找到公众号入口");
                return false;
            }

            sleep(CONFIG.WAIT_TIME.MEDIUM);

            // 查找指定公众号
            let account = this.findPublicAccount(accountName);
            if (account) {
                account.click();
                sleep(CONFIG.WAIT_TIME.MEDIUM);
                toast(`成功打开公众号: ${accountName}`);
                return true;
            }

            toast(`未找到公众号: ${accountName}`);
            return false;

        } catch (error) {
            toast("打开公众号失败: " + error.message);
            return false;
        }
    }

    // 查找公众号
    static findPublicAccount(name) {
        // 直接查找
        let account = text(name).findOne(2000) || textContains(name).findOne(2000);
        if (account) return account;

        // 滑动查找
        let scrollable = scrollable(true).findOne(3000);
        if (scrollable) {
            for (let i = 0; i < 5; i++) {
                account = text(name).findOne(500) || textContains(name).findOne(500);
                if (account) return account;

                scrollable.scrollForward();
                sleep(CONFIG.WAIT_TIME.SHORT);
            }
        }

        return null;
    }

    // 阅读公众号文章
    static readArticles(count = 3) {
        try {
            toast(`准备阅读${count}篇文章`);

            for (let i = 0; i < count; i++) {
                // 查找文章链接
                let articles = className("android.view.View").clickable(true).find();
                if (articles.length > i) {
                    articles[i].click();
                    sleep(CONFIG.WAIT_TIME.LONG);

                    // 模拟阅读
                    this.simulateReading();

                    // 返回
                    back();
                    sleep(CONFIG.WAIT_TIME.MEDIUM);

                    toast(`已阅读第${i + 1}篇文章`);
                }
            }

            return true;

        } catch (error) {
            toast("阅读文章失败: " + error.message);
            return false;
        }
    }

    // 模拟阅读文章
    static simulateReading() {
        // 滑动阅读
        for (let i = 0; i < 3; i++) {
            swipe(device.width / 2, device.height * 0.8,
                  device.width / 2, device.height * 0.3, 1500);
            sleep(CONFIG.WAIT_TIME.MEDIUM);
        }
    }
}

// 联系人操作类
class ContactOperations {

    // 查看通讯录
    static viewContacts() {
        try {
            toast("正在查看通讯录");

            if (!WeChatUtils.switchToTab("通讯录")) {
                return false;
            }

            sleep(CONFIG.WAIT_TIME.MEDIUM);
            toast("已进入通讯录");
            return true;

        } catch (error) {
            toast("查看通讯录失败: " + error.message);
            return false;
        }
    }

    // 添加好友
    static addFriend(searchKeyword) {
        try {
            toast(`正在搜索并添加好友: ${searchKeyword}`);

            // 切换到通讯录
            if (!this.viewContacts()) {
                return false;
            }

            // 点击添加朋友
            if (!WeChatUtils.clickElement("添加朋友") && !WeChatUtils.clickElement("+")) {
                toast("未找到添加朋友按钮");
                return false;
            }

            sleep(CONFIG.WAIT_TIME.MEDIUM);

            // 搜索用户
            let searchBox = className("EditText").findOne(3000);
            if (searchBox) {
                searchBox.setText(searchKeyword);
                sleep(CONFIG.WAIT_TIME.SHORT);

                // 点击搜索
                key(66); // 回车键
                sleep(CONFIG.WAIT_TIME.LONG);

                // 点击添加到通讯录
                if (WeChatUtils.clickElement("添加到通讯录")) {
                    sleep(CONFIG.WAIT_TIME.SHORT);

                    // 发送添加请求
                    if (WeChatUtils.clickElement("发送")) {
                        toast(`已发送好友请求给: ${searchKeyword}`);
                        return true;
                    }
                }
            }

            toast("添加好友失败");
            return false;

        } catch (error) {
            toast("添加好友失败: " + error.message);
            return false;
        }
    }
}

// 主演示类
class WeChatDemo {

    // 运行完整演示
    static async runFullDemo() {
        try {
            toast("开始微信操作演示");

            // 1. 启动微信
            if (!await WeChatUtils.launchWeChat()) {
                return false;
            }

            // 2. 发送消息演示
            this.showDemoStep("消息发送演示");
            // ChatOperations.sendMessage("文件传输助手", "这是一条测试消息 " + new Date().toLocaleTimeString());

            sleep(CONFIG.WAIT_TIME.MEDIUM);

            // 3. 朋友圈演示
            this.showDemoStep("朋友圈操作演示");
            MomentsOperations.viewMoments();

            sleep(CONFIG.WAIT_TIME.LONG);

            // 4. 公众号演示
            this.showDemoStep("公众号操作演示");
            // PublicAccountOperations.openPublicAccount("人民日报");

            sleep(CONFIG.WAIT_TIME.MEDIUM);

            // 5. 通讯录演示
            this.showDemoStep("通讯录查看演示");
            ContactOperations.viewContacts();

            toast("演示完成！");
            return true;

        } catch (error) {
            toast("演示过程出错: " + error.message);
            console.error("演示错误:", error);
            return false;
        }
    }

    // 显示演示步骤
    static showDemoStep(stepName) {
        toast("=== " + stepName + " ===");
        console.log("执行步骤:", stepName);
        sleep(CONFIG.WAIT_TIME.SHORT);
    }

    // 单独功能测试
    static testSingleFunction() {
        // 可以在这里测试单个功能
        console.log("开始单功能测试");

        // 示例：测试搜索功能
        WeChatUtils.launchWeChat().then(() => {
            WeChatUtils.searchInWeChat("文件传输助手");
        });
    }
}

// 使用示例和主入口
function main() {
    try {
        console.log("微信操作演示脚本启动");

        // 显示操作菜单
        let choice = dialogs.select("选择操作", [
            "完整演示",
            "发送消息",
            "查看朋友圈",
            "打开公众号",
            "查看通讯录",
            "搜索功能",
            "退出"
        ]);

        switch(choice) {
            case 0:
                WeChatDemo.runFullDemo();
                break;
            case 1:
                let contact = dialogs.rawInput("输入联系人名称", "文件传输助手");
                let message = dialogs.rawInput("输入消息内容", "Hello from Auto.js!");
                if (contact && message) {
                    WeChatUtils.launchWeChat().then(() => {
                        ChatOperations.sendMessage(contact, message);
                    });
                }
                break;
            case 2:
                WeChatUtils.launchWeChat().then(() => {
                    MomentsOperations.viewMoments();
                });
                break;
            case 3:
                let accountName = dialogs.rawInput("输入公众号名称", "人民日报");
                if (accountName) {
                    WeChatUtils.launchWeChat().then(() => {
                        PublicAccountOperations.openPublicAccount(accountName);
                    });
                }
                break;
            case 4:
                WeChatUtils.launchWeChat().then(() => {
                    ContactOperations.viewContacts();
                });
                break;
            case 5:
                let keyword = dialogs.rawInput("输入搜索关键词", "测试");
                if (keyword) {
                    WeChatUtils.launchWeChat().then(() => {
                        WeChatUtils.searchInWeChat(keyword);
                    });
                }
                break;
            case 6:
                toast("退出脚本");
                exit();
                break;
            default:
                toast("未选择操作");
                break;
        }

    } catch (error) {
        toast("脚本执行出错: " + error.message);
        console.error("主函数错误:", error);
    }
}

// 启动脚本
main();
