// 请求必要的权限
auto.waitFor();

// 主函数
function openOfficialAccount(accountName) {
    try {
        // 1. 启动微信
        launchApp("com.tencent.mm"); // 使用包名更可靠
        toast("正在打开微信...");
        sleep(3000);

        // 等待微信启动完成
        if (!waitForApp("com.tencent.mm", 10000)) {
            toast("微信启动失败");
            return false;
        }

        // 2. 确保在微信主界面，点击微信tab
        let wechatTab = text("微信").findOne(3000) || desc("微信").findOne(3000);
        if (wechatTab) {
            wechatTab.click();
            sleep(1000);
        }


        // 5. 查找指定公众号
        let account = findOfficialAccount(accountName);
        if (account) {
            toast("找到公众号：" + accountName);
            account.click();
            sleep(2000);
            return true;
        } else {
            toast("未找到公众号：" + accountName);
            return false;
        }
    } catch (error) {
        toast("执行出错：" + error.message);
        console.error(error);
        return false;
    }
}

// 辅助函数：等待应用启动
function waitForApp(packageName, timeout) {
    let startTime = Date.now();
    while (Date.now() - startTime < timeout) {
        if (currentPackage() === packageName) {
            return true;
        }
        sleep(500);
    }
    return false;
}

// 辅助函数：点击文本或描述匹配的元素
function clickTextOrDesc(targetText) {
    // 先尝试文本匹配
    let element = text(targetText).findOne(2000);
    if (element && element.clickable()) {
        element.click();
        return true;
    }

    // 再尝试描述匹配
    element = desc(targetText).findOne(2000);
    if (element && element.clickable()) {
        element.click();
        return true;
    }

    // 尝试查找包含该文本的可点击父元素
    element = text(targetText).findOne(2000);
    if (element) {
        let clickableParent = element.parent();
        while (clickableParent && !clickableParent.clickable()) {
            clickableParent = clickableParent.parent();
        }
        if (clickableParent) {
            clickableParent.click();
            return true;
        }
    }

    toast("未找到可点击元素：" + targetText);
    return false;
}

// 辅助函数：查找公众号
function findOfficialAccount(name) {
    toast("正在查找公众号：" + name);

    // 方法1：直接查找文本
    let account = text(name).findOne(2000);
    if (account) {
        account.click();
        console.log("通过文本找到公众号：" + name);
        return account;
    }

    // 方法2：查找包含该文本的元素
    let accounts = textContains(name).find();
    if (accounts.length > 0) {
        console.log("通过包含文本找到公众号：" + name);
        return accounts[0];
    }

    // 方法3：滑动查找
    toast("开始滑动查找...");
    let scrollableElement = scrollable(true).findOne(2000);
    if (!scrollableElement) {
        // 尝试查找列表容器
        scrollableElement = className("ListView").findOne(2000) ||
                           className("RecyclerView").findOne(2000) ||
                           id("listview").findOne(2000);
    }

    if (scrollableElement) {
        let maxScrolls = 8; // 增加滑动次数
        for (let i = 0; i < maxScrolls; i++) {
            // 检查当前屏幕是否有目标公众号
            account = text(name).findOne(500);
            if (account) {
                console.log("滑动后找到公众号：" + name);
                return account;
            }

            // 检查包含文本的元素
            accounts = textContains(name).find();
            if (accounts.length > 0) {
                console.log("滑动后通过包含文本找到公众号：" + name);
                return accounts[0];
            }

            // 向下滑动
            if (i < maxScrolls - 1) { // 最后一次不滑动
                scrollableElement.scrollForward();
                sleep(1000);
            }
        }
    }

    // 方法4：使用描述查找
    account = desc(name).findOne(1000);
    if (account) {
        console.log("通过描述找到公众号：" + name);
        return account;
    }

    console.log("未找到公众号：" + name);
    return null;
}

// 主执行函数
function main() {
    try {
        // 使用示例 - 打开"人民日报"公众号
        let success = openOfficialAccount("服务号");

        if (success) {
            // 验证是否成功打开公众号页面
            sleep(2000);
            if (text("进入公众号").exists() ||
                text("关注").exists() ||
                text("已关注").exists() ||
                desc("进入公众号").exists()) {
                toast("成功进入公众号页面");
                console.log("成功打开公众号");
            } else {
                toast("可能已进入公众号，但界面检测不确定");
                console.log("界面状态不确定");
            }
        } else {
            toast("打开公众号失败");
            console.log("打开公众号失败");
        }
    } catch (error) {
        toast("脚本执行出错：" + error.message);
        console.error("脚本执行出错：", error);
    }
}

// 执行主函数
main();