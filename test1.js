// 请求必要的权限
auto.waitFor();
requestScreenCapture();

// 主函数
function openOfficialAccount(accountName) {
    // 1. 启动微信
    launchApp("微信");
    toast("正在打开微信...");
    sleep(3000);
    click("微信");
    sleep(3000);
    
    // 2. 确保在微信主界面
    // if (!text("微信").exists()) {
    //     toast("未检测到微信主界面");
    //     back(); // 尝试返回主界面
    //     sleep(1000);
    // }
    
    // 3. 点击通讯录
    clickTextOrDesc("通讯录");
    sleep(2000);
    
    // 4. 点击公众号
    clickTextOrDesc("公众号");
    sleep(2000);
    
    // 5. 查找指定公众号
    let account = findOfficialAccount(accountName);
    if (account) {
        toast("找到公众号：" + accountName);
        account.click();
        sleep(2000);
        return true;
    } else {
        toast("未找到公众号：" + accountName);
        return false;
    }
}

// 辅助函数：点击文本或描述匹配的元素
function clickTextOrDesc(targetText) {
    let element = text(targetText).findOne(2000) || desc(targetText).findOne(2000);
    if (element) {
        element.click();
        return true;
    }
    toast("未找到元素：" + targetText);
    return false;
}

// 辅助函数：查找公众号
function findOfficialAccount(name) {
    // 方法1：直接查找文本
    let account = text(name).findOne(2000);
    if (account) return account;
    
    // 方法2：滑动查找
    let list = id("listview").findOne(2000);
    if (list) {
        for (let i = 0; i < 5; i++) {
            let account = text(name).findOne(500);
            if (account) return account;
            swipe(list.bounds().centerX(), list.bounds().bottom - 50, 
                  list.bounds().centerX(), list.bounds().top + 50, 500);
            sleep(800);
        }
    }
    
    // 方法3：使用描述查找
    return desc(name).findOne(2000);
}

// 使用示例 - 打开"人民日报"公众号
openOfficialAccount("人民日报");

// 验证是否成功打开
if (text("进入公众号").exists()) {
    toast("成功进入公众号页面");
} else {
    toast("未能进入公众号页面");
}