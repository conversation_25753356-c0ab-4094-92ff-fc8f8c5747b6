/**
 * Auto.js 获取点击坐标的多种方法
 */

// 请求必要的权限
auto.waitFor();
requestScreenCapture();

// 方法1: 通过触摸监听获取坐标
function getCoordinatesByTouch() {
    toast("请在3秒后点击屏幕，将记录点击坐标");
    sleep(3000);
    
    var touchStarted = false;
    var coordinates = [];
    
    // 监听触摸事件
    events.observeTouch();
    events.onTouch(function(point) {
        if (point.action == "down") {
            coordinates.push({x: point.x, y: point.y});
            toast("记录坐标: (" + point.x + ", " + point.y + ")");
            console.log("点击坐标: x=" + point.x + ", y=" + point.y);
        }
    });
    
    toast("开始监听触摸，点击屏幕记录坐标，5秒后自动停止");
    sleep(5000);
    
    // 停止监听
    events.removeAllListeners();
    
    toast("共记录了 " + coordinates.length + " 个坐标点");
    return coordinates;
}

// 方法2: 通过元素获取坐标
function getCoordinatesByElement(elementText) {
    try {
        // 查找元素
        var element = text(elementText).findOne(3000);
        if (!element) {
            element = desc(elementText).findOne(3000);
        }
        if (!element) {
            element = textContains(elementText).findOne(3000);
        }
        
        if (element) {
            var bounds = element.bounds();
            var centerX = bounds.centerX();
            var centerY = bounds.centerY();
            
            toast("元素 '" + elementText + "' 的中心坐标: (" + centerX + ", " + centerY + ")");
            console.log("元素坐标信息:");
            console.log("- 中心点: (" + centerX + ", " + centerY + ")");
            console.log("- 左上角: (" + bounds.left + ", " + bounds.top + ")");
            console.log("- 右下角: (" + bounds.right + ", " + bounds.bottom + ")");
            console.log("- 宽度: " + bounds.width() + ", 高度: " + bounds.height());
            
            return {
                center: {x: centerX, y: centerY},
                bounds: {
                    left: bounds.left,
                    top: bounds.top,
                    right: bounds.right,
                    bottom: bounds.bottom,
                    width: bounds.width(),
                    height: bounds.height()
                }
            };
        } else {
            toast("未找到元素: " + elementText);
            return null;
        }
    } catch (error) {
        toast("获取元素坐标失败: " + error.message);
        return null;
    }
}

// 方法3: 获取屏幕上所有可点击元素的坐标
function getAllClickableCoordinates() {
    try {
        toast("正在扫描所有可点击元素...");
        
        var clickableElements = clickable(true).find();
        var coordinates = [];
        
        for (var i = 0; i < clickableElements.length; i++) {
            var element = clickableElements[i];
            var bounds = element.bounds();
            var centerX = bounds.centerX();
            var centerY = bounds.centerY();
            
            // 获取元素文本信息
            var elementText = element.text() || element.desc() || "无文本";
            var className = element.className() || "未知类名";
            
            coordinates.push({
                index: i,
                text: elementText,
                className: className,
                center: {x: centerX, y: centerY},
                bounds: {
                    left: bounds.left,
                    top: bounds.top,
                    right: bounds.right,
                    bottom: bounds.bottom
                }
            });
            
            console.log("元素 " + i + ": " + elementText + " (" + centerX + ", " + centerY + ")");
        }
        
        toast("找到 " + coordinates.length + " 个可点击元素");
        return coordinates;
        
    } catch (error) {
        toast("扫描可点击元素失败: " + error.message);
        return [];
    }
}

// 方法4: 实时显示触摸坐标（悬浮窗）
function showRealTimeCoordinates() {
    try {
        // 创建悬浮窗
        var window = floaty.window(
            <frame gravity="center" bg="#44000000">
                <text id="coord" textColor="white" textSize="16sp" text="坐标: (0, 0)"/>
            </frame>
        );
        
        window.setPosition(100, 100);
        
        // 监听触摸事件
        events.observeTouch();
        events.onTouch(function(point) {
            if (point.action == "move" || point.action == "down") {
                ui.run(function() {
                    window.coord.setText("坐标: (" + Math.round(point.x) + ", " + Math.round(point.y) + ")");
                });
            }
        });
        
        toast("实时坐标显示已启动，移动手指查看坐标");
        toast("10秒后自动关闭");
        
        // 10秒后关闭
        setTimeout(function() {
            events.removeAllListeners();
            window.close();
            toast("实时坐标显示已关闭");
        }, 10000);
        
    } catch (error) {
        toast("启动实时坐标显示失败: " + error.message);
    }
}

// 方法5: 点击指定坐标并验证
function clickAndVerifyCoordinates(x, y) {
    try {
        toast("即将点击坐标: (" + x + ", " + y + ")");
        sleep(1000);
        
        // 点击坐标
        click(x, y);
        
        toast("已点击坐标: (" + x + ", " + y + ")");
        console.log("点击坐标: (" + x + ", " + y + ")");
        
        return true;
    } catch (error) {
        toast("点击坐标失败: " + error.message);
        return false;
    }
}

// 方法6: 批量获取指定文本元素的坐标
function getMultipleElementCoordinates(textArray) {
    var results = [];
    
    for (var i = 0; i < textArray.length; i++) {
        var elementText = textArray[i];
        var coords = getCoordinatesByElement(elementText);
        if (coords) {
            results.push({
                text: elementText,
                coordinates: coords
            });
        }
    }
    
    return results;
}

// 主菜单函数
function main() {
    try {
        var choice = dialogs.select("选择获取坐标的方法", [
            "触摸监听获取坐标",
            "通过元素文本获取坐标", 
            "获取所有可点击元素坐标",
            "实时显示触摸坐标",
            "点击指定坐标",
            "批量获取元素坐标",
            "退出"
        ]);
        
        switch(choice) {
            case 0:
                getCoordinatesByTouch();
                break;
            case 1:
                var elementText = dialogs.rawInput("输入要查找的元素文本", "微信");
                if (elementText) {
                    getCoordinatesByElement(elementText);
                }
                break;
            case 2:
                getAllClickableCoordinates();
                break;
            case 3:
                showRealTimeCoordinates();
                break;
            case 4:
                var x = parseInt(dialogs.rawInput("输入X坐标", "500"));
                var y = parseInt(dialogs.rawInput("输入Y坐标", "1000"));
                if (!isNaN(x) && !isNaN(y)) {
                    clickAndVerifyCoordinates(x, y);
                }
                break;
            case 5:
                var texts = dialogs.rawInput("输入要查找的元素文本(用逗号分隔)", "微信,通讯录,发现,我");
                if (texts) {
                    var textArray = texts.split(",");
                    var results = getMultipleElementCoordinates(textArray);
                    toast("获取了 " + results.length + " 个元素的坐标");
                    console.log("批量坐标结果:", JSON.stringify(results, null, 2));
                }
                break;
            case 6:
                toast("退出脚本");
                exit();
                break;
            default:
                toast("未选择操作");
                break;
        }
        
    } catch (error) {
        toast("脚本执行出错: " + error.message);
        console.error("主函数错误:", error);
    }
}

// 启动脚本
main();
