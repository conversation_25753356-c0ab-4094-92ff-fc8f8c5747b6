launchApp("微信")
sleep(1000);
click("微信");
sleep(1000);
openChatWith("文件传输助手");

function openChatWith(contactName) {
    // 点击底部"微信"标签回到聊天列表
    clickWithRetry(text("微信").clickable(true));
    sleep(1000);
    
    // 在聊天列表中查找联系人
    let chatItem = null;
    for (let i = 0; i < 5; i++) {
        chatItem = descMatches(contactName).findOne(1000) || 
                  textMatches(contactName).findOne(1000);
        if (chatItem) break;
        
        // 向下滑动查找
        swipe(device.width / 2, device.height * 0.7, 
              device.width / 2, device.height * 0.3, 500);
        sleep(800);
    }
    
    if (chatItem) {
        chatItem.click();
        toast("已打开与 " + contactName + " 的聊天");
        sleep(1500);
        return true;
    } else {
        toast("未找到联系人: " + contactName);
        return false;
    }
}

function clickWithRetry(selector, maxRetry = 3, delay = 1000) {
    for (let i = 0; i < maxRetry; i++) {
        let element = selector.findOne(1000);
        if (element) {
            element.click();
            return true;
        }
        sleep(delay);
    }
    return false;
}